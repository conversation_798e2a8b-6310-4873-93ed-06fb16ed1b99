#!/usr/bin/env python3
"""
Simple test to verify the FastAPI service works.
"""

import asyncio
from fastapi import FastAP<PERSON>
from pydantic import BaseModel

# Simple test models
class TestRequest(BaseModel):
    message: str

class TestResponse(BaseModel):
    echo: str
    status: str

# Create simple FastAPI app
app = FastAPI(title="DroidRun Test Service")

@app.get("/")
async def root():
    return {"message": "DroidRun Test Service is running"}

@app.post("/test", response_model=TestResponse)
async def test_endpoint(request: TestRequest):
    return TestResponse(
        echo=f"Received: {request.message}",
        status="success"
    )

if __name__ == "__main__":
    import uvicorn
    print("Starting simple test service on http://localhost:8000")
    print("Visit http://localhost:8000/docs for API documentation")
    uvicorn.run(app, host="0.0.0.0", port=8000)
