"""
Configuration management for the DroidRun FastAPI service.
"""

import os
from typing import Optional
from pydantic_settings import BaseSettings


class Settings(BaseSettings):
    """Application settings."""
    
    # API Configuration
    api_title: str = "DroidRun HTTP Service"
    api_description: str = "FastAPI service for DroidRun Android automation"
    api_version: str = "1.0.0"
    
    # Server Configuration
    host: str = "0.0.0.0"
    port: int = 43826
    reload: bool = True
    
    # LLM Configuration
    default_llm_provider: str = "DeepSeek"
    default_llm_model: str = "deepseek-v3-250324"
    default_llm_temperature: float = 0.2
    default_llm_api_base: str = "https://ark.cn-beijing.volces.com/api/v3"
    default_llm_api_key: str = "1444135e-36b0-4f40-9130-7524530ea32c"
    
    # Task Configuration
    default_max_steps: int = 15
    default_timeout: int = 1000
    default_max_retries: int = 3
    default_reasoning: bool = True
    default_debug: bool = False
    
    # Callback Configuration
    callback_timeout: int = 30
    callback_retries: int = 3
    
    # Task Management
    task_cleanup_interval: int = 3600  # 1 hour
    max_completed_tasks: int = 1000
    
    # Logging
    log_level: str = "INFO"
    
    class Config:
        env_file = ".env"
        env_prefix = "DROIDRUN_"


# Global settings instance
settings = Settings()
