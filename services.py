"""
Task management and execution services for the DroidRun FastAPI service.
"""

import asyncio
import logging
import time
import httpx
from typing import Dict, Optional
from concurrent.futures import ThreadPoolExecutor
from contextlib import asynccontextmanager

from llama_index.llms.deepseek import DeepSeek
from droidrun.agent.droid import DroidAgent

from models import TaskInfo, TaskState, TaskResultRequest, TaskResultArgModel, AuthModel
from config import settings

logger = logging.getLogger(__name__)


class TaskManager:
    """Manages task execution and lifecycle."""

    def __init__(self):
        self.tasks: Dict[str, TaskInfo] = {}
        self.running_tasks: Dict[str, asyncio.Task] = {}
        self.executor = ThreadPoolExecutor(max_workers=10)
        self._cleanup_task: Optional[asyncio.Task] = None

    async def start(self):
        """Start the task manager."""
        logger.info("Starting TaskManager...")
        # Start cleanup task
        self._cleanup_task = asyncio.create_task(self._cleanup_completed_tasks())

    async def stop(self):
        """Stop the task manager."""
        logger.info("Stopping TaskManager...")

        # Cancel all running tasks
        for job_id, task in self.running_tasks.items():
            logger.info(f"Cancelling task {job_id}")
            task.cancel()

        # Wait for tasks to complete
        if self.running_tasks:
            await asyncio.gather(*self.running_tasks.values(), return_exceptions=True)

        # Cancel cleanup task
        if self._cleanup_task:
            self._cleanup_task.cancel()

        # Shutdown executor
        self.executor.shutdown(wait=True)

    def create_task(
        self,
        job_id: str,
        eac_id: str,
        content: str,
        device: Optional[str] = None,
        callback_url: Optional[str] = None
    ) -> TaskInfo:
        """Create a new task."""
        if job_id in self.tasks:
            raise ValueError(f"Task with job_id {job_id} already exists")

        task_info = TaskInfo(
            job_id=job_id,
            eac_id=eac_id,
            content=content,
            device=device,
            state=TaskState.PENDING,
            created_at=time.time(),
            callback_url=callback_url
        )

        self.tasks[job_id] = task_info
        logger.info(f"Created task {job_id}: {content}")
        return task_info

    async def execute_task(self, job_id: str) -> None:
        """Execute a task asynchronously."""
        if job_id not in self.tasks:
            raise ValueError(f"Task {job_id} not found")

        task_info = self.tasks[job_id]

        if task_info.state != TaskState.PENDING:
            raise ValueError(f"Task {job_id} is not in pending state")

        # Update task state
        task_info.state = TaskState.RUNNING
        task_info.started_at = time.time()

        # Create and start the execution task
        execution_task = asyncio.create_task(self._run_droid_agent(task_info))
        self.running_tasks[job_id] = execution_task

        logger.info(f"Started execution of task {job_id}")

    async def _run_droid_agent(self, task_info: TaskInfo) -> None:
        """Run the DroidAgent for a task."""
        try:
            logger.info(f"Executing DroidAgent for task {task_info.job_id}")

            # Create LLM instance
            llm = DeepSeek(
                model=settings.default_llm_model,
                api_key=settings.default_llm_api_key,
                api_base=settings.default_llm_api_base,
                temperature=settings.default_llm_temperature
            )

            # Create DroidAgent
            agent = DroidAgent(
                goal=task_info.content,
                llm=llm,
                max_steps=settings.default_max_steps,
                device_serial=task_info.device,
                timeout=settings.default_timeout,
                max_retries=settings.default_max_retries,
                reasoning=settings.default_reasoning,
                debug=settings.default_debug
            )

            # Execute the agent
            result = await agent.run()

            # Update task state based on result
            if result.get('success', False):
                task_info.state = TaskState.SUCCESS
                task_info.result_message = result.get('reason', 'Task completed successfully')
            else:
                task_info.state = TaskState.FAILED
                task_info.result_message = result.get('reason', 'Task execution failed')

            task_info.completed_at = time.time()

            logger.info(f"Task {task_info.job_id} completed with state {task_info.state}")

        except asyncio.CancelledError:
            logger.info(f"Task {task_info.job_id} was cancelled")
            task_info.state = TaskState.FAILED
            task_info.result_message = "Task was cancelled"
            task_info.completed_at = time.time()
            raise

        except Exception as e:
            logger.error(f"Error executing task {task_info.job_id}: {e}")
            task_info.state = TaskState.FAILED
            task_info.result_message = f"Execution error: {str(e)}"
            task_info.completed_at = time.time()

        finally:
            # Remove from running tasks
            self.running_tasks.pop(task_info.job_id, None)

            # Send callback if configured
            if task_info.callback_url:
                await self._send_callback(task_info)

    async def _send_callback(self, task_info: TaskInfo) -> None:
        """Send task result callback."""
        try:
            logger.info(f"Sending callback for task {task_info.job_id}")

            # Prepare callback payload
            callback_data = TaskResultRequest(
                v="1.0.0",
                auth=AuthModel(appId="droidrun", nonce="callback"),
                arg=TaskResultArgModel(
                    eacId=task_info.eac_id,
                    jobId=task_info.job_id,
                    eventType="Autonomous",
                    state=task_info.state,
                    msg=task_info.result_message or ""
                )
            )

            # Send HTTP POST request
            async with httpx.AsyncClient(timeout=settings.callback_timeout) as client:
                response = await client.post(
                    task_info.callback_url,
                    json=callback_data.model_dump(),
                    headers={"Content-Type": "application/json"}
                )
                response.raise_for_status()

            logger.info(f"Callback sent successfully for task {task_info.job_id}")

        except Exception as e:
            logger.error(f"Failed to send callback for task {task_info.job_id}: {e}")

    async def stop_task(self, job_id: str) -> bool:
        """Stop a running task."""
        if job_id not in self.tasks:
            return False

        task_info = self.tasks[job_id]

        if task_info.state != TaskState.RUNNING:
            return False

        # Cancel the running task
        if job_id in self.running_tasks:
            self.running_tasks[job_id].cancel()
            logger.info(f"Cancelled task {job_id}")
            return True

        return False

    def get_task(self, job_id: str) -> Optional[TaskInfo]:
        """Get task information."""
        return self.tasks.get(job_id)

    def get_all_tasks(self) -> Dict[str, TaskInfo]:
        """Get all tasks."""
        return self.tasks.copy()

    async def _cleanup_completed_tasks(self) -> None:
        """Periodically cleanup completed tasks."""
        while True:
            try:
                await asyncio.sleep(settings.task_cleanup_interval)

                # Get completed tasks sorted by completion time
                completed_tasks = [
                    (job_id, task) for job_id, task in self.tasks.items()
                    if task.state in [TaskState.SUCCESS, TaskState.FAILED] and task.completed_at
                ]

                if len(completed_tasks) > settings.max_completed_tasks:
                    # Sort by completion time and remove oldest
                    completed_tasks.sort(key=lambda x: x[1].completed_at)
                    to_remove = len(completed_tasks) - settings.max_completed_tasks

                    for i in range(to_remove):
                        job_id = completed_tasks[i][0]
                        del self.tasks[job_id]
                        logger.info(f"Cleaned up completed task {job_id}")

            except asyncio.CancelledError:
                break
            except Exception as e:
                logger.error(f"Error during task cleanup: {e}")


# Global task manager instance
task_manager = TaskManager()


@asynccontextmanager
async def lifespan(_app):
    """Application lifespan manager."""
    # Startup
    await task_manager.start()
    yield
    # Shutdown
    await task_manager.stop()
