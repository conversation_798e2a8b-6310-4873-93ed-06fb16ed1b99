#!/usr/bin/env python3
"""
Test script to verify all imports work correctly.
"""

def test_imports():
    """Test all imports."""
    try:
        print("Testing basic imports...")
        import asyncio
        import time
        import logging
        print("✓ Basic imports OK")
        
        print("Testing FastAPI imports...")
        from fastapi import FastAPI, HTTPException, BackgroundTasks
        from fastapi.middleware.cors import CORSMiddleware
        print("✓ FastAPI imports OK")
        
        print("Testing pydantic imports...")
        from pydantic import BaseModel, Field
        from pydantic_settings import BaseSettings
        print("✓ Pydantic imports OK")
        
        print("Testing httpx import...")
        import httpx
        print("✓ httpx import OK")
        
        print("Testing droidrun imports...")
        from llama_index.llms.deepseek import DeepSeek
        from droidrun.agent.droid import DroidAgent
        print("✓ DroidRun imports OK")
        
        print("Testing local module imports...")
        from models import TaskInfo, TaskState, RunTaskRequest
        print("✓ models.py import OK")
        
        from config import settings
        print("✓ config.py import OK")
        print(f"  - Default port: {settings.port}")
        print(f"  - Default LLM: {settings.default_llm_provider}")
        
        from services import TaskManager, task_manager
        print("✓ services.py import OK")
        
        print("\n🎉 All imports successful!")
        return True
        
    except Exception as e:
        print(f"❌ Import error: {e}")
        import traceback
        traceback.print_exc()
        return False


def test_basic_functionality():
    """Test basic functionality."""
    try:
        print("\nTesting basic functionality...")
        
        # Test TaskManager creation
        from services import TaskManager
        tm = TaskManager()
        print("✓ TaskManager creation OK")
        
        # Test task creation
        task_info = tm.create_task(
            job_id="test-123",
            eac_id="eac-test",
            content="Test task",
            device=None
        )
        print(f"✓ Task creation OK: {task_info.job_id}")
        
        # Test task retrieval
        retrieved = tm.get_task("test-123")
        assert retrieved is not None
        assert retrieved.job_id == "test-123"
        print("✓ Task retrieval OK")
        
        print("\n🎉 Basic functionality test passed!")
        return True
        
    except Exception as e:
        print(f"❌ Functionality error: {e}")
        import traceback
        traceback.print_exc()
        return False


if __name__ == "__main__":
    print("=== DroidRun HTTP Service Import Test ===\n")
    
    success = test_imports()
    if success:
        success = test_basic_functionality()
    
    if success:
        print("\n✅ All tests passed! The service should be ready to run.")
    else:
        print("\n❌ Some tests failed. Please check the errors above.")
