#!/usr/bin/env python3
"""
Test script for the new DroidRun HTTP service with async wrapper.
"""

import asyncio
import time
import json
from async_wrapper import create_droidrun_wrapper


async def test_async_wrapper():
    """Test the async wrapper functionality."""
    print("=== Testing Async Wrapper ===\n")
    
    # Test with mock wrapper first
    print("1. Testing Mock Wrapper:")
    mock_wrapper = create_droidrun_wrapper(use_mock=True)
    
    try:
        result = await mock_wrapper.execute_task(
            goal="Test mock execution",
            device_serial="test_device"
        )
        print(f"   Mock result: {json.dumps(result, indent=2)}")
    except Exception as e:
        print(f"   Mock error: {e}")
    
    # Test with real wrapper (if available)
    print("\n2. Testing Real Wrapper:")
    real_wrapper = create_droidrun_wrapper(use_mock=False)
    
    try:
        # Use a simple task for testing
        result = await real_wrapper.execute_task(
            goal="打开设置应用",
            device_serial="cyojfeayhqytaypj",
            max_steps=5,
            timeout=60,
            reasoning=False  # Use direct mode for faster testing
        )
        print(f"   Real result: {json.dumps(result, indent=2)}")
    except Exception as e:
        print(f"   Real wrapper error: {e}")
        import traceback
        traceback.print_exc()


async def test_service_imports():
    """Test service imports."""
    print("\n=== Testing Service Imports ===\n")
    
    try:
        print("Testing config import...")
        from config import settings
        print(f"✓ Config loaded - port: {settings.port}")
        
        print("Testing models import...")
        from models import TaskInfo, TaskState
        print("✓ Models imported")
        
        print("Testing services import...")
        from services import TaskManager
        print("✓ Services imported")
        
        print("Testing task manager creation...")
        tm = TaskManager()
        print("✓ TaskManager created")
        
        print("Testing task creation...")
        task = tm.create_task(
            job_id="test-123",
            eac_id="eac-test",
            content="Test task",
            device="test_device"
        )
        print(f"✓ Task created: {task.job_id}")
        
        return True
        
    except Exception as e:
        print(f"❌ Import/creation error: {e}")
        import traceback
        traceback.print_exc()
        return False


async def test_fastapi_app():
    """Test FastAPI app creation."""
    print("\n=== Testing FastAPI App ===\n")
    
    try:
        print("Testing app import...")
        from app import app
        print("✓ App imported successfully")
        
        print("Testing simple app import...")
        from simple_app import app as simple_app
        print("✓ Simple app imported successfully")
        
        return True
        
    except Exception as e:
        print(f"❌ App import error: {e}")
        import traceback
        traceback.print_exc()
        return False


async def main():
    """Main test function."""
    print("=== DroidRun HTTP Service Test Suite ===\n")
    
    # Test service imports
    imports_ok = await test_service_imports()
    if not imports_ok:
        print("❌ Service imports failed, skipping other tests")
        return
    
    # Test FastAPI app
    app_ok = await test_fastapi_app()
    if not app_ok:
        print("❌ FastAPI app test failed")
    
    # Test async wrapper
    await test_async_wrapper()
    
    print("\n=== Test Suite Complete ===")
    print("✅ Basic functionality tests passed!")
    print("\nTo start the service:")
    print("  python simple_app.py    # For simple mode")
    print("  python start_server.py  # For full mode")


if __name__ == "__main__":
    asyncio.run(main())
