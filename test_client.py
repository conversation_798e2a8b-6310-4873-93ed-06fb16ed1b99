#!/usr/bin/env python3
"""
Test client for the DroidRun FastAPI HTTP service.
"""

import requests
import time
import json
from typing import Dict, Any


class DroidRunClient:
    """Client for interacting with DroidRun HTTP service."""
    
    def __init__(self, base_url: str = "http://localhost:43826"):
        self.base_url = base_url.rstrip('/')
        
    def submit_task(
        self,
        job_id: str,
        content: str,
        eac_id: str = "eac-001",
        device: str = None,
        app_id: str = "test_app",
        nonce: str = "test_nonce"
    ) -> Dict[str, Any]:
        """Submit a new task."""
        task_data = {
            "v": "1.0.0",
            "auth": {
                "appId": app_id,
                "nonce": nonce
            },
            "arg": {
                "site": "test-site",
                "eacId": eac_id,
                "jobId": job_id,
                "eventType": "Autonomous",
                "content": content,
                "device": device
            }
        }
        
        response = requests.post(
            f"{self.base_url}/api/aia/run-task",
            json=task_data,
            headers={"Content-Type": "application/json"}
        )
        response.raise_for_status()
        return response.json()
        
    def get_task_status(self, job_id: str) -> Dict[str, Any]:
        """Get task status."""
        response = requests.get(f"{self.base_url}/api/aia/task-status/{job_id}")
        response.raise_for_status()
        return response.json()
        
    def stop_task(self, job_id: str) -> Dict[str, Any]:
        """Stop a task."""
        response = requests.post(
            f"{self.base_url}/api/aia/stop-task",
            json={"job_id": job_id},
            headers={"Content-Type": "application/json"}
        )
        response.raise_for_status()
        return response.json()
        
    def list_tasks(self) -> Dict[str, Any]:
        """List all tasks."""
        response = requests.get(f"{self.base_url}/api/aia/tasks")
        response.raise_for_status()
        return response.json()
        
    def health_check(self) -> Dict[str, Any]:
        """Check service health."""
        response = requests.get(f"{self.base_url}/health")
        response.raise_for_status()
        return response.json()
        
    def wait_for_task_completion(self, job_id: str, timeout: int = 300, poll_interval: int = 2) -> Dict[str, Any]:
        """Wait for task to complete."""
        start_time = time.time()
        
        while time.time() - start_time < timeout:
            status = self.get_task_status(job_id)
            task_state = status["data"]["state"]
            
            print(f"Task {job_id} state: {task_state}")
            
            if task_state in [1, 3]:  # SUCCESS or FAILED
                return status
                
            time.sleep(poll_interval)
            
        raise TimeoutError(f"Task {job_id} did not complete within {timeout} seconds")


def main():
    """Main test function."""
    client = DroidRunClient()
    
    print("=== DroidRun HTTP Service Test ===\n")
    
    # Health check
    print("1. Health Check:")
    try:
        health = client.health_check()
        print(f"   Status: {health['status']}")
        print(f"   Tasks: {health['tasks']}")
    except Exception as e:
        print(f"   Error: {e}")
        return
    
    # Generate unique job ID
    job_id = f"test-job-{int(time.time())}"
    
    # Submit task
    print(f"\n2. Submitting Task (ID: {job_id}):")
    try:
        task_content = "打开设置应用并查看Android版本"
        device_serial = "cyojfeayhqytaypj"  # 使用demo.py中的设备
        
        result = client.submit_task(
            job_id=job_id,
            content=task_content,
            device=device_serial
        )
        print(f"   Response: {json.dumps(result, indent=2)}")
    except Exception as e:
        print(f"   Error: {e}")
        return
    
    # Monitor task status
    print(f"\n3. Monitoring Task Status:")
    try:
        final_status = client.wait_for_task_completion(job_id, timeout=120)
        print(f"   Final Status: {json.dumps(final_status, indent=2)}")
    except Exception as e:
        print(f"   Error: {e}")
    
    # List all tasks
    print(f"\n4. Listing All Tasks:")
    try:
        tasks = client.list_tasks()
        print(f"   Total tasks: {tasks['data']['total']}")
        print(f"   Running tasks: {tasks['data']['running']}")
    except Exception as e:
        print(f"   Error: {e}")
    
    print("\n=== Test Complete ===")


def test_stop_task():
    """Test task stopping functionality."""
    client = DroidRunClient()
    
    print("=== Task Stop Test ===\n")
    
    # Submit a long-running task
    job_id = f"stop-test-{int(time.time())}"
    task_content = "执行一个长时间的任务，比如浏览多个应用"
    
    print(f"1. Submitting long-running task (ID: {job_id}):")
    try:
        result = client.submit_task(job_id=job_id, content=task_content)
        print(f"   Task submitted: {result}")
    except Exception as e:
        print(f"   Error: {e}")
        return
    
    # Wait a bit for task to start
    print("\n2. Waiting for task to start...")
    time.sleep(5)
    
    # Check status
    try:
        status = client.get_task_status(job_id)
        print(f"   Task state: {status['data']['state']}")
    except Exception as e:
        print(f"   Error: {e}")
    
    # Stop the task
    print(f"\n3. Stopping task {job_id}:")
    try:
        stop_result = client.stop_task(job_id)
        print(f"   Stop result: {json.dumps(stop_result, indent=2)}")
    except Exception as e:
        print(f"   Error: {e}")
    
    # Check final status
    print(f"\n4. Final task status:")
    try:
        final_status = client.get_task_status(job_id)
        print(f"   Final state: {final_status['data']['state']}")
        print(f"   Result message: {final_status['data']['resultMessage']}")
    except Exception as e:
        print(f"   Error: {e}")
    
    print("\n=== Stop Test Complete ===")


if __name__ == "__main__":
    import sys
    
    if len(sys.argv) > 1 and sys.argv[1] == "stop":
        test_stop_task()
    else:
        main()
        
    print("\nTo test task stopping, run: python test_client.py stop")
