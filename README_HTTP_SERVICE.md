# DroidRun FastAPI HTTP Service

这是一个基于FastAPI的HTTP服务，用于封装DroidRun Android自动化框架，提供异步任务执行、状态查询、结果上报和任务停止等功能。

## 功能特性

- ✅ 异步任务执行：提交任务后立即返回，后台执行
- ✅ 任务状态查询：实时查询任务执行状态
- ✅ 任务停止/取消：支持终止正在运行的任务
- ✅ 结果回调：任务完成后自动调用回调接口上报结果
- ✅ 设备选择：支持指定Android设备执行任务
- ✅ 标准化响应：统一的JSON响应格式

## 快速开始

### 1. 启动服务

```bash
# 方式1：使用启动脚本
python start_server.py

# 方式2：直接使用uvicorn
uvicorn app:app --host 0.0.0.0 --port 43826 --reload

# 方式3：运行app.py
python app.py
```

服务启动后，可以访问：
- API文档：http://localhost:43826/docs
- 健康检查：http://localhost:43826/health

### 2. API接口

#### 执行任务 - POST /api/aia/run-task

提交一个新任务进行异步执行。

**请求示例：**
```json
{
  "v": "1.0.0",
  "auth": {
    "appId": "wbb2dej3",
    "nonce": "g1wbfhdx9IkDBOVQFHFkPjUUxYijErxT"
  },
  "arg": {
    "site": "eac-android-any",
    "eacId": "eac-0112",
    "jobId": "job-2933236482858576299",
    "eventType": "Autonomous",
    "content": "查看安卓版本号",
    "device": "cyojfeayhqytaypj"
  }
}
```

**响应示例：**
```json
{
  "code": 0,
  "msg": "Task created and started successfully",
  "data": {
    "jobId": "job-2933236482858576299",
    "state": "queued"
  }
}
```

#### 查询任务状态 - GET /api/aia/task-status/{job_id}

查询指定任务的执行状态。

**响应示例：**
```json
{
  "code": 0,
  "msg": "Task status retrieved successfully",
  "data": {
    "jobId": "job-2933236482858576299",
    "eacId": "eac-0112",
    "content": "查看安卓版本号",
    "device": "cyojfeayhqytaypj",
    "state": 1,
    "createdAt": 1703123456.789,
    "startedAt": 1703123457.123,
    "completedAt": 1703123467.456,
    "resultMessage": "执行结果是 Android 14"
  }
}
```

#### 停止任务 - POST /api/aia/stop-task

停止正在运行的任务。

**请求示例：**
```json
{
  "job_id": "job-2933236482858576299"
}
```

**响应示例：**
```json
{
  "code": 0,
  "msg": "Task stopped successfully",
  "data": {
    "jobId": "job-2933236482858576299",
    "stopped": true
  }
}
```

#### 列出所有任务 - GET /api/aia/tasks

获取所有任务的列表。

**响应示例：**
```json
{
  "code": 0,
  "msg": "Tasks retrieved successfully",
  "data": {
    "tasks": [...],
    "total": 10,
    "running": 2
  }
}
```

### 3. 任务状态说明

- `0` - PENDING：任务已创建，等待执行
- `1` - SUCCESS：任务执行成功
- `2` - RUNNING：任务正在执行中
- `3` - FAILED：任务执行失败

### 4. 配置说明

可以通过环境变量或`.env`文件配置服务参数：

```bash
# 服务配置
DROIDRUN_HOST=0.0.0.0
DROIDRUN_PORT=43826
DROIDRUN_RELOAD=true

# LLM配置
DROIDRUN_DEFAULT_LLM_PROVIDER=DeepSeek
DROIDRUN_DEFAULT_LLM_MODEL=deepseek-v3-250324
DROIDRUN_DEFAULT_LLM_API_KEY=your_api_key_here
DROIDRUN_DEFAULT_LLM_API_BASE=https://ark.cn-beijing.volces.com/api/v3

# 任务配置
DROIDRUN_DEFAULT_MAX_STEPS=15
DROIDRUN_DEFAULT_TIMEOUT=1000
DROIDRUN_DEFAULT_MAX_RETRIES=3
DROIDRUN_DEFAULT_REASONING=true

# 日志配置
DROIDRUN_LOG_LEVEL=INFO
```

## 使用示例

### Python客户端示例

```python
import requests
import time

# 服务地址
BASE_URL = "http://localhost:43826"

# 提交任务
task_data = {
    "v": "1.0.0",
    "auth": {
        "appId": "test_app",
        "nonce": "test_nonce"
    },
    "arg": {
        "site": "test-site",
        "eacId": "eac-001",
        "jobId": f"job-{int(time.time())}",
        "eventType": "Autonomous",
        "content": "打开设置应用",
        "device": "your_device_serial"
    }
}

# 提交任务
response = requests.post(f"{BASE_URL}/api/aia/run-task", json=task_data)
print("Task submitted:", response.json())

job_id = task_data["arg"]["jobId"]

# 查询任务状态
while True:
    response = requests.get(f"{BASE_URL}/api/aia/task-status/{job_id}")
    status = response.json()
    print("Task status:", status)
    
    if status["data"]["state"] in [1, 3]:  # SUCCESS or FAILED
        break
        
    time.sleep(2)
```

### curl示例

```bash
# 提交任务
curl -X POST "http://localhost:43826/api/aia/run-task" \
  -H "Content-Type: application/json" \
  -d '{
    "v": "1.0.0",
    "auth": {
      "appId": "test_app",
      "nonce": "test_nonce"
    },
    "arg": {
      "site": "test-site",
      "eacId": "eac-001",
      "jobId": "job-123456",
      "eventType": "Autonomous",
      "content": "打开设置应用",
      "device": "your_device_serial"
    }
  }'

# 查询任务状态
curl "http://localhost:43826/api/aia/task-status/job-123456"

# 停止任务
curl -X POST "http://localhost:43826/api/aia/stop-task" \
  -H "Content-Type: application/json" \
  -d '{"job_id": "job-123456"}'
```

## 注意事项

1. 确保Android设备已连接并启用了ADB调试
2. 确保DroidRun Portal应用已安装并启用了无障碍服务
3. 配置正确的LLM API密钥和端点
4. 任务执行可能需要一些时间，请耐心等待
5. 建议在生产环境中配置适当的认证和授权机制

## 故障排除

1. **服务启动失败**：检查端口是否被占用，确认依赖包已正确安装
2. **任务执行失败**：检查设备连接状态，确认LLM配置正确
3. **设备无响应**：确认DroidRun Portal应用正在运行且无障碍服务已启用
4. **API调用失败**：检查请求格式是否正确，查看服务日志获取详细错误信息
