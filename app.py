"""
FastAPI application for DroidRun HTTP service.
"""

import logging
import time
from fastapi import FastAPI, HTTPException, BackgroundTasks
from fastapi.middleware.cors import CORSMiddleware

from models import (
    RunTaskRequest, StandardResponse, TaskStatusResponse,
    StopTaskRequest, StopTaskResponse
)
from services import task_manager, lifespan
from config import settings

# Configure logging
logging.basicConfig(
    level=getattr(logging, settings.log_level.upper()),
    format='%(asctime)s - %(name)s - %(levelname)s - %(message)s'
)
logger = logging.getLogger(__name__)

# Create FastAPI app
app = FastAPI(
    title=settings.api_title,
    description=settings.api_description,
    version=settings.api_version,
    lifespan=lifespan
)

# Add CORS middleware
app.add_middleware(
    CORSMiddleware,
    allow_origins=["*"],
    allow_credentials=True,
    allow_methods=["*"],
    allow_headers=["*"],
)


@app.get("/")
async def root():
    """Root endpoint."""
    return {
        "message": "DroidRun HTTP Service",
        "version": settings.api_version,
        "status": "running"
    }


@app.get("/health")
async def health_check():
    """Health check endpoint."""
    return {
        "status": "healthy",
        "timestamp": time.time(),
        "tasks": {
            "total": len(task_manager.get_all_tasks()),
            "running": len(task_manager.running_tasks)
        }
    }


@app.post("/api/aia/run-task", response_model=StandardResponse)
async def run_task(request: RunTaskRequest, background_tasks: BackgroundTasks):
    """
    Execute a task asynchronously.

    This endpoint accepts a task request, creates the task, and starts execution
    in the background. It returns immediately with a success response.
    """
    try:
        logger.info(f"Received task request for job {request.arg.jobId}")

        # Extract task parameters
        job_id = request.arg.jobId
        eac_id = request.arg.eacId
        content = request.arg.content
        device = request.arg.device

        # Validate required parameters
        if not job_id:
            raise HTTPException(status_code=400, detail="jobId is required")
        if not content:
            raise HTTPException(status_code=400, detail="content is required")

        # Create task
        try:
            task_manager.create_task(
                job_id=job_id,
                eac_id=eac_id,
                content=content,
                device=device
            )
        except ValueError as e:
            raise HTTPException(status_code=400, detail=str(e))

        # Start task execution in background
        background_tasks.add_task(task_manager.execute_task, job_id)

        logger.info(f"Task {job_id} created and queued for execution")

        return StandardResponse(
            code=0,
            msg="Task created and started successfully",
            data={"jobId": job_id, "state": "queued"}
        )

    except HTTPException:
        raise
    except Exception as e:
        logger.error(f"Error processing task request: {e}")
        raise HTTPException(status_code=500, detail=f"Internal server error: {str(e)}")


@app.get("/api/aia/task-status/{job_id}", response_model=TaskStatusResponse)
async def get_task_status(job_id: str):
    """
    Get the status of a specific task.
    """
    try:
        task_info = task_manager.get_task(job_id)

        if not task_info:
            raise HTTPException(status_code=404, detail=f"Task {job_id} not found")

        # Prepare response data
        data = {
            "jobId": task_info.job_id,
            "eacId": task_info.eac_id,
            "content": task_info.content,
            "device": task_info.device,
            "state": task_info.state.value,
            "createdAt": task_info.created_at,
            "startedAt": task_info.started_at,
            "completedAt": task_info.completed_at,
            "resultMessage": task_info.result_message
        }

        return TaskStatusResponse(
            code=0,
            msg="Task status retrieved successfully",
            data=data
        )

    except HTTPException:
        raise
    except Exception as e:
        logger.error(f"Error getting task status: {e}")
        raise HTTPException(status_code=500, detail=f"Internal server error: {str(e)}")


@app.post("/api/aia/stop-task", response_model=StopTaskResponse)
async def stop_task(request: StopTaskRequest):
    """
    Stop a running task.
    """
    try:
        job_id = request.job_id

        if not job_id:
            raise HTTPException(status_code=400, detail="job_id is required")

        # Check if task exists
        task_info = task_manager.get_task(job_id)
        if not task_info:
            raise HTTPException(status_code=404, detail=f"Task {job_id} not found")

        # Try to stop the task
        stopped = await task_manager.stop_task(job_id)

        if stopped:
            logger.info(f"Task {job_id} stopped successfully")
            return StopTaskResponse(
                code=0,
                msg="Task stopped successfully",
                data={"jobId": job_id, "stopped": True}
            )
        else:
            # Task was not running
            return StopTaskResponse(
                code=1,
                msg=f"Task {job_id} is not running (current state: {task_info.state.name})",
                data={"jobId": job_id, "stopped": False, "state": task_info.state.value}
            )

    except HTTPException:
        raise
    except Exception as e:
        logger.error(f"Error stopping task: {e}")
        raise HTTPException(status_code=500, detail=f"Internal server error: {str(e)}")


@app.get("/api/aia/tasks", response_model=TaskStatusResponse)
async def list_tasks():
    """
    List all tasks.
    """
    try:
        all_tasks = task_manager.get_all_tasks()

        # Convert tasks to response format
        tasks_data = []
        for _, task_info in all_tasks.items():
            tasks_data.append({
                "jobId": task_info.job_id,
                "eacId": task_info.eac_id,
                "content": task_info.content,
                "device": task_info.device,
                "state": task_info.state.value,
                "createdAt": task_info.created_at,
                "startedAt": task_info.started_at,
                "completedAt": task_info.completed_at,
                "resultMessage": task_info.result_message
            })

        return TaskStatusResponse(
            code=0,
            msg="Tasks retrieved successfully",
            data={
                "tasks": tasks_data,
                "total": len(tasks_data),
                "running": len(task_manager.running_tasks)
            }
        )

    except Exception as e:
        logger.error(f"Error listing tasks: {e}")
        raise HTTPException(status_code=500, detail=f"Internal server error: {str(e)}")


if __name__ == "__main__":
    import uvicorn
    import time

    # Configure uvicorn to use asyncio instead of uvloop to avoid nest_asyncio conflicts
    uvicorn.run(
        "app:app",
        host=settings.host,
        port=settings.port,
        reload=settings.reload,
        loop="asyncio"  # Use asyncio instead of uvloop
    )
