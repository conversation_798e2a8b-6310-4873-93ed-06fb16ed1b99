"""
Simplified FastAPI application for DroidRun HTTP service (without llama_index dependency).
"""

import logging
import time
import asyncio
from typing import Dict, Optional
from fastapi import Fast<PERSON><PERSON>, HTTPException, BackgroundTasks
from fastapi.middleware.cors import CORSMiddleware

from models import (
    RunTaskRequest, StandardResponse, TaskStatusResponse, 
    StopTaskRequest, StopTaskResponse, TaskState, TaskInfo
)
from config import settings

# Configure logging
logging.basicConfig(
    level=getattr(logging, settings.log_level.upper()),
    format='%(asctime)s - %(name)s - %(levelname)s - %(message)s'
)
logger = logging.getLogger(__name__)

# Simple task manager without DroidAgent dependency
class SimpleTaskManager:
    """Simple task manager for testing."""
    
    def __init__(self):
        self.tasks: Dict[str, TaskInfo] = {}
        self.running_tasks: Dict[str, asyncio.Task] = {}
        
    def create_task(
        self,
        job_id: str,
        eac_id: str,
        content: str,
        device: Optional[str] = None
    ) -> TaskInfo:
        """Create a new task."""
        if job_id in self.tasks:
            raise ValueError(f"Task with job_id {job_id} already exists")
            
        task_info = TaskInfo(
            job_id=job_id,
            eac_id=eac_id,
            content=content,
            device=device,
            state=TaskState.PENDING,
            created_at=time.time()
        )
        
        self.tasks[job_id] = task_info
        logger.info(f"Created task {job_id}: {content}")
        return task_info
        
    async def execute_task(self, job_id: str) -> None:
        """Execute a task (mock implementation)."""
        if job_id not in self.tasks:
            raise ValueError(f"Task {job_id} not found")
            
        task_info = self.tasks[job_id]
        
        if task_info.state != TaskState.PENDING:
            raise ValueError(f"Task {job_id} is not in pending state")
            
        # Update task state
        task_info.state = TaskState.RUNNING
        task_info.started_at = time.time()
        
        # Create and start the execution task
        execution_task = asyncio.create_task(self._mock_execution(task_info))
        self.running_tasks[job_id] = execution_task
        
        logger.info(f"Started execution of task {job_id}")
        
    async def _mock_execution(self, task_info: TaskInfo) -> None:
        """Mock task execution."""
        try:
            logger.info(f"Executing mock task {task_info.job_id}")
            
            # Simulate task execution time
            await asyncio.sleep(5)
            
            # Mock success
            task_info.state = TaskState.SUCCESS
            task_info.result_message = f"Mock execution completed for: {task_info.content}"
            task_info.completed_at = time.time()
            
            logger.info(f"Task {task_info.job_id} completed successfully")
            
        except asyncio.CancelledError:
            logger.info(f"Task {task_info.job_id} was cancelled")
            task_info.state = TaskState.FAILED
            task_info.result_message = "Task was cancelled"
            task_info.completed_at = time.time()
            raise
            
        except Exception as e:
            logger.error(f"Error executing task {task_info.job_id}: {e}")
            task_info.state = TaskState.FAILED
            task_info.result_message = f"Execution error: {str(e)}"
            task_info.completed_at = time.time()
            
        finally:
            # Remove from running tasks
            self.running_tasks.pop(task_info.job_id, None)
            
    async def stop_task(self, job_id: str) -> bool:
        """Stop a running task."""
        if job_id not in self.tasks:
            return False
            
        task_info = self.tasks[job_id]
        
        if task_info.state != TaskState.RUNNING:
            return False
            
        # Cancel the running task
        if job_id in self.running_tasks:
            self.running_tasks[job_id].cancel()
            logger.info(f"Cancelled task {job_id}")
            return True
            
        return False
        
    def get_task(self, job_id: str) -> Optional[TaskInfo]:
        """Get task information."""
        return self.tasks.get(job_id)
        
    def get_all_tasks(self) -> Dict[str, TaskInfo]:
        """Get all tasks."""
        return self.tasks.copy()

# Global task manager instance
task_manager = SimpleTaskManager()

# Create FastAPI app
app = FastAPI(
    title=settings.api_title,
    description=settings.api_description,
    version=settings.api_version
)

# Add CORS middleware
app.add_middleware(
    CORSMiddleware,
    allow_origins=["*"],
    allow_credentials=True,
    allow_methods=["*"],
    allow_headers=["*"],
)

@app.get("/")
async def root():
    """Root endpoint."""
    return {
        "message": "DroidRun HTTP Service (Simple Mode)",
        "version": settings.api_version,
        "status": "running",
        "mode": "simple"
    }

@app.get("/health")
async def health_check():
    """Health check endpoint."""
    return {
        "status": "healthy",
        "timestamp": time.time(),
        "tasks": {
            "total": len(task_manager.get_all_tasks()),
            "running": len(task_manager.running_tasks)
        }
    }

@app.post("/api/aia/run-task", response_model=StandardResponse)
async def run_task(request: RunTaskRequest, background_tasks: BackgroundTasks):
    """Execute a task asynchronously."""
    try:
        logger.info(f"Received task request for job {request.arg.jobId}")
        
        # Extract task parameters
        job_id = request.arg.jobId
        eac_id = request.arg.eacId
        content = request.arg.content
        device = request.arg.device
        
        # Validate required parameters
        if not job_id:
            raise HTTPException(status_code=400, detail="jobId is required")
        if not content:
            raise HTTPException(status_code=400, detail="content is required")
            
        # Create task
        try:
            task_manager.create_task(
                job_id=job_id,
                eac_id=eac_id,
                content=content,
                device=device
            )
        except ValueError as e:
            raise HTTPException(status_code=400, detail=str(e))
            
        # Start task execution in background
        background_tasks.add_task(task_manager.execute_task, job_id)
        
        logger.info(f"Task {job_id} created and queued for execution")
        
        return StandardResponse(
            code=0,
            msg="Task created and started successfully",
            data={"jobId": job_id, "state": "queued"}
        )
        
    except HTTPException:
        raise
    except Exception as e:
        logger.error(f"Error processing task request: {e}")
        raise HTTPException(status_code=500, detail=f"Internal server error: {str(e)}")

@app.get("/api/aia/task-status/{job_id}", response_model=TaskStatusResponse)
async def get_task_status(job_id: str):
    """Get the status of a specific task."""
    try:
        task_info = task_manager.get_task(job_id)
        
        if not task_info:
            raise HTTPException(status_code=404, detail=f"Task {job_id} not found")
            
        # Prepare response data
        data = {
            "jobId": task_info.job_id,
            "eacId": task_info.eac_id,
            "content": task_info.content,
            "device": task_info.device,
            "state": task_info.state.value,
            "createdAt": task_info.created_at,
            "startedAt": task_info.started_at,
            "completedAt": task_info.completed_at,
            "resultMessage": task_info.result_message
        }
        
        return TaskStatusResponse(
            code=0,
            msg="Task status retrieved successfully",
            data=data
        )
        
    except HTTPException:
        raise
    except Exception as e:
        logger.error(f"Error getting task status: {e}")
        raise HTTPException(status_code=500, detail=f"Internal server error: {str(e)}")

@app.post("/api/aia/stop-task", response_model=StopTaskResponse)
async def stop_task(request: StopTaskRequest):
    """Stop a running task."""
    try:
        job_id = request.job_id
        
        if not job_id:
            raise HTTPException(status_code=400, detail="job_id is required")
            
        # Check if task exists
        task_info = task_manager.get_task(job_id)
        if not task_info:
            raise HTTPException(status_code=404, detail=f"Task {job_id} not found")
            
        # Try to stop the task
        stopped = await task_manager.stop_task(job_id)
        
        if stopped:
            logger.info(f"Task {job_id} stopped successfully")
            return StopTaskResponse(
                code=0,
                msg="Task stopped successfully",
                data={"jobId": job_id, "stopped": True}
            )
        else:
            # Task was not running
            return StopTaskResponse(
                code=1,
                msg=f"Task {job_id} is not running (current state: {task_info.state.name})",
                data={"jobId": job_id, "stopped": False, "state": task_info.state.value}
            )
            
    except HTTPException:
        raise
    except Exception as e:
        logger.error(f"Error stopping task: {e}")
        raise HTTPException(status_code=500, detail=f"Internal server error: {str(e)}")

@app.get("/api/aia/tasks", response_model=TaskStatusResponse)
async def list_tasks():
    """List all tasks."""
    try:
        all_tasks = task_manager.get_all_tasks()
        
        # Convert tasks to response format
        tasks_data = []
        for _, task_info in all_tasks.items():
            tasks_data.append({
                "jobId": task_info.job_id,
                "eacId": task_info.eac_id,
                "content": task_info.content,
                "device": task_info.device,
                "state": task_info.state.value,
                "createdAt": task_info.created_at,
                "startedAt": task_info.started_at,
                "completedAt": task_info.completed_at,
                "resultMessage": task_info.result_message
            })
            
        return TaskStatusResponse(
            code=0,
            msg="Tasks retrieved successfully",
            data={
                "tasks": tasks_data,
                "total": len(tasks_data),
                "running": len(task_manager.running_tasks)
            }
        )
        
    except Exception as e:
        logger.error(f"Error listing tasks: {e}")
        raise HTTPException(status_code=500, detail=f"Internal server error: {str(e)}")

if __name__ == "__main__":
    import uvicorn
    
    print(f"Starting DroidRun HTTP Service (Simple Mode)...")
    print(f"Server will run on http://{settings.host}:{settings.port}")
    print(f"API documentation available at http://{settings.host}:{settings.port}/docs")
    
    uvicorn.run(
        app,
        host=settings.host,
        port=settings.port,
        log_level=settings.log_level.lower()
    )
