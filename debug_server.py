#!/usr/bin/env python3
"""
Debug version of the server to identify startup issues.
"""

import sys
import traceback

def test_imports():
    """Test all imports step by step."""
    try:
        print("Testing basic imports...")
        import logging
        import time
        print("✓ Basic imports OK")
        
        print("Testing FastAPI...")
        from fastapi import FastAPI, HTTPException, BackgroundTasks
        from fastapi.middleware.cors import CORSMiddleware
        print("✓ FastAPI imports OK")
        
        print("Testing config...")
        from config import settings
        print(f"✓ Config loaded - port: {settings.port}")
        
        print("Testing models...")
        from models import RunTaskRequest, StandardResponse
        print("✓ Models imported OK")
        
        print("Testing services...")
        from services import task_manager, lifespan
        print("✓ Services imported OK")
        
        return True
        
    except Exception as e:
        print(f"❌ Import failed: {e}")
        traceback.print_exc()
        return False

def test_simple_app():
    """Test creating a simple FastAPI app."""
    try:
        print("\nTesting simple FastAPI app...")
        from fastapi import FastAPI
        
        app = FastAPI(title="Debug Test")
        
        @app.get("/")
        async def root():
            return {"message": "Debug server working"}
            
        print("✓ Simple app created OK")
        return app
        
    except Exception as e:
        print(f"❌ App creation failed: {e}")
        traceback.print_exc()
        return None

def test_full_app():
    """Test creating the full app."""
    try:
        print("\nTesting full app import...")
        from app import app
        print("✓ Full app imported OK")
        return app
        
    except Exception as e:
        print(f"❌ Full app import failed: {e}")
        traceback.print_exc()
        return None

def main():
    print("=== DroidRun Server Debug ===\n")
    
    # Test imports
    if not test_imports():
        print("❌ Import test failed")
        return
    
    # Test simple app
    simple_app = test_simple_app()
    if simple_app is None:
        print("❌ Simple app test failed")
        return
    
    # Test full app
    full_app = test_full_app()
    if full_app is None:
        print("❌ Full app test failed")
        return
    
    print("\n✅ All tests passed!")
    
    # Try to start the server
    try:
        print("\nStarting debug server on port 8001...")
        import uvicorn
        uvicorn.run(simple_app, host="127.0.0.1", port=8001, log_level="debug")
        
    except Exception as e:
        print(f"❌ Server start failed: {e}")
        traceback.print_exc()

if __name__ == "__main__":
    main()
