"""
Async wrapper to handle DroidRun execution without nest_asyncio conflicts.
"""

import asyncio
import logging
import subprocess
import sys
import json
import tempfile
import os
from typing import Dict, Any, Optional

logger = logging.getLogger(__name__)


class DroidRunAsyncWrapper:
    """Wrapper to execute DroidRun in a separate process to avoid async conflicts."""
    
    def __init__(self):
        self.python_executable = sys.executable
        
    async def execute_task(
        self,
        goal: str,
        device_serial: Optional[str] = None,
        max_steps: int = 15,
        timeout: int = 1000,
        max_retries: int = 3,
        reasoning: bool = True,
        debug: bool = False,
        llm_provider: str = "DeepSeek",
        llm_model: str = "deepseek-v3-250324",
        llm_api_key: str = "1444135e-36b0-4f40-9130-7524530ea32c",
        llm_api_base: str = "https://ark.cn-beijing.volces.com/api/v3",
        llm_temperature: float = 0.2
    ) -> Dict[str, Any]:
        """Execute a DroidRun task in a separate process."""
        
        # Create a temporary script to run DroidRun
        script_content = f'''
import asyncio
import sys
import json
import traceback

async def main():
    try:
        from llama_index.llms.deepseek import DeepSeek
        from droidrun.agent.droid import DroidAgent
        
        # Create LLM instance
        llm = DeepSeek(
            model="{llm_model}",
            api_key="{llm_api_key}",
            api_base="{llm_api_base}",
            temperature={llm_temperature}
        )
        
        # Create DroidAgent
        agent = DroidAgent(
            goal="{goal}",
            llm=llm,
            max_steps={max_steps},
            device_serial="{device_serial}" if "{device_serial}" != "None" else None,
            timeout={timeout},
            max_retries={max_retries},
            reasoning={reasoning},
            debug={debug}
        )
        
        # Execute the agent
        result = await agent.run()
        
        # Return result as JSON
        print(json.dumps({{
            "success": True,
            "result": result
        }}))
        
    except Exception as e:
        print(json.dumps({{
            "success": False,
            "error": str(e),
            "traceback": traceback.format_exc()
        }}))

if __name__ == "__main__":
    asyncio.run(main())
'''
        
        # Write script to temporary file
        with tempfile.NamedTemporaryFile(mode='w', suffix='.py', delete=False) as f:
            f.write(script_content)
            script_path = f.name
            
        try:
            # Execute the script in a subprocess
            logger.info(f"Executing DroidRun task in subprocess: {goal}")
            
            process = await asyncio.create_subprocess_exec(
                self.python_executable, script_path,
                stdout=asyncio.subprocess.PIPE,
                stderr=asyncio.subprocess.PIPE,
                cwd=os.getcwd()
            )
            
            # Wait for completion with timeout
            try:
                stdout, stderr = await asyncio.wait_for(
                    process.communicate(), 
                    timeout=timeout + 60  # Add extra time for process overhead
                )
            except asyncio.TimeoutError:
                process.kill()
                await process.wait()
                return {
                    "success": False,
                    "error": "Task execution timed out",
                    "timeout": True
                }
            
            # Parse output
            stdout_str = stdout.decode('utf-8').strip()
            stderr_str = stderr.decode('utf-8').strip()
            
            if process.returncode != 0:
                logger.error(f"DroidRun process failed with return code {process.returncode}")
                logger.error(f"stderr: {stderr_str}")
                return {
                    "success": False,
                    "error": f"Process failed with return code {process.returncode}",
                    "stderr": stderr_str
                }
            
            # Try to parse JSON output
            try:
                result = json.loads(stdout_str)
                return result
            except json.JSONDecodeError:
                logger.error(f"Failed to parse JSON output: {stdout_str}")
                return {
                    "success": False,
                    "error": "Failed to parse execution result",
                    "stdout": stdout_str,
                    "stderr": stderr_str
                }
                
        finally:
            # Clean up temporary file
            try:
                os.unlink(script_path)
            except OSError:
                pass


class MockDroidRunWrapper:
    """Mock wrapper for testing without actual DroidRun execution."""
    
    async def execute_task(self, goal: str, **kwargs) -> Dict[str, Any]:
        """Mock task execution."""
        logger.info(f"Mock execution of task: {goal}")
        
        # Simulate execution time
        await asyncio.sleep(2)
        
        # Return mock success result
        return {
            "success": True,
            "result": {
                "success": True,
                "reason": f"Mock execution completed for: {goal}",
                "steps": 3,
                "trajectory": []
            }
        }


# Factory function to create the appropriate wrapper
def create_droidrun_wrapper(use_mock: bool = False) -> DroidRunAsyncWrapper:
    """Create a DroidRun wrapper."""
    if use_mock:
        return MockDroidRunWrapper()
    else:
        return DroidRunAsyncWrapper()
