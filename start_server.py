#!/usr/bin/env python3
"""
Startup script for the DroidRun FastAPI HTTP service.
"""

import uvicorn
from config import settings

if __name__ == "__main__":
    print(f"Starting DroidRun HTTP Service...")
    print(f"Server will run on http://{settings.host}:{settings.port}")
    print(f"API documentation available at http://{settings.host}:{settings.port}/docs")

    uvicorn.run(
        "app:app",
        host=settings.host,
        port=settings.port,
        reload=settings.reload,
        log_level=settings.log_level.lower(),
        loop="asyncio"  # Use asyncio instead of uvloop to avoid nest_asyncio conflicts
    )
