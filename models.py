"""
Pydantic models for the DroidRun FastAPI service.
"""

from pydantic import BaseModel, Field
from typing import Optional, Dict, Any
from enum import Enum


class TaskState(int, Enum):
    """Task execution states."""
    PENDING = 0
    SUCCESS = 1
    RUNNING = 2
    FAILED = 3


class AuthModel(BaseModel):
    """Authentication model."""
    appId: str = Field(..., description="Application ID")
    nonce: str = Field(..., description="Random nonce for request")


class TaskArgModel(BaseModel):
    """Task arguments model."""
    site: str = Field(..., description="Site identifier")
    eacId: str = Field(..., description="EAC identifier")
    jobId: str = Field(..., description="Job identifier")
    eventType: str = Field(..., description="Event type")
    content: str = Field(..., description="Task goal/content")
    device: Optional[str] = Field(None, description="Device serial number")


class RunTaskRequest(BaseModel):
    """Request model for running a task."""
    v: str = Field(..., description="API version")
    auth: AuthModel = Field(..., description="Authentication data")
    arg: TaskArgModel = Field(..., description="Task arguments")


class StandardResponse(BaseModel):
    """Standard response model."""
    code: int = Field(..., description="Response code (0 for success)")
    msg: str = Field("", description="Response message")
    data: Dict[str, Any] = Field(default_factory=dict, description="Response data")


class TaskResultArgModel(BaseModel):
    """Task result arguments model."""
    eacId: str = Field(..., description="EAC identifier")
    jobId: str = Field(..., description="Job identifier")
    eventType: str = Field(..., description="Event type")
    state: TaskState = Field(..., description="Task execution state")
    msg: str = Field(..., description="Execution result message")


class TaskResultRequest(BaseModel):
    """Request model for task result callback."""
    v: str = Field(..., description="API version")
    auth: AuthModel = Field(..., description="Authentication data")
    arg: TaskResultArgModel = Field(..., description="Task result arguments")


class TaskResultResponse(BaseModel):
    """Response model for task result callback."""
    errorCode: int = Field(..., description="Error code (0 for success)")
    errorMessage: str = Field(..., description="Error message")


class TaskInfo(BaseModel):
    """Task information model."""
    job_id: str = Field(..., description="Job identifier")
    eac_id: str = Field(..., description="EAC identifier")
    content: str = Field(..., description="Task content/goal")
    device: Optional[str] = Field(None, description="Device serial number")
    state: TaskState = Field(TaskState.PENDING, description="Task state")
    created_at: float = Field(..., description="Task creation timestamp")
    started_at: Optional[float] = Field(None, description="Task start timestamp")
    completed_at: Optional[float] = Field(None, description="Task completion timestamp")
    result_message: Optional[str] = Field(None, description="Task result message")
    callback_url: Optional[str] = Field(None, description="Callback URL for result reporting")


class TaskStatusResponse(BaseModel):
    """Response model for task status query."""
    code: int = Field(..., description="Response code")
    msg: str = Field("", description="Response message")
    data: Dict[str, Any] = Field(default_factory=dict, description="Task status data")


class StopTaskRequest(BaseModel):
    """Request model for stopping a task."""
    job_id: str = Field(..., description="Job identifier to stop")


class StopTaskResponse(BaseModel):
    """Response model for stopping a task."""
    code: int = Field(..., description="Response code")
    msg: str = Field("", description="Response message")
    data: Dict[str, Any] = Field(default_factory=dict, description="Response data")
